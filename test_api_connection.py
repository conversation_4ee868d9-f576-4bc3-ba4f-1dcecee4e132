#!/usr/bin/env python3
"""
简单的API连接测试脚本
"""
import os
import sys
from dotenv import load_dotenv
from openai import OpenAI

# 加载环境变量
load_dotenv()

def test_api_connection():
    """测试API连接"""
    print("开始测试API连接...")
    
    # 获取配置
    api_key = os.getenv("OPENAI_API_KEY")
    api_base = os.getenv("OPENAI_API_BASE")
    model_name = os.getenv("MODEL_NAME", "qwq-32b")
    
    print(f"API Key: {api_key[:10]}..." if api_key else "API Key: 未设置")
    print(f"API Base: {api_base}")
    print(f"Model Name: {model_name}")
    
    if not api_key:
        print("❌ 错误：未设置OPENAI_API_KEY")
        return False
    
    if not api_base:
        print("❌ 错误：未设置OPENAI_API_BASE")
        return False
    
    try:
        # 初始化客户端
        client = OpenAI(
            api_key=api_key,
            base_url=api_base
        )
        print("✓ OpenAI客户端初始化成功")
        
        # 发送简单的测试请求
        print("发送测试请求...")
        response = client.chat.completions.create(
            model=model_name,
            messages=[
                {"role": "system", "content": "你是一个有用的助手。"},
                {"role": "user", "content": "请回答：1+1等于多少？"}
            ],
            timeout=30
        )
        
        print("✓ API调用成功")
        
        # 检查响应结构
        print(f"响应类型: {type(response)}")
        print(f"响应对象: {response}")
        
        if hasattr(response, 'choices'):
            print(f"choices字段存在: {response.choices}")
            print(f"choices类型: {type(response.choices)}")
            print(f"choices长度: {len(response.choices) if response.choices else 0}")
            
            if response.choices and len(response.choices) > 0:
                choice = response.choices[0]
                print(f"第一个choice: {choice}")
                print(f"choice类型: {type(choice)}")
                
                if hasattr(choice, 'message'):
                    message = choice.message
                    print(f"message字段: {message}")
                    print(f"message类型: {type(message)}")
                    
                    if hasattr(message, 'content'):
                        content = message.content
                        print(f"content字段: {content}")
                        print(f"content类型: {type(content)}")
                        print(f"✓ 测试成功，收到回复: {content}")
                        return True
                    else:
                        print("❌ message没有content字段")
                        return False
                else:
                    print("❌ choice没有message字段")
                    return False
            else:
                print("❌ choices为空")
                return False
        else:
            print("❌ 响应没有choices字段")
            return False
            
    except Exception as e:
        print(f"❌ API调用失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_api_connection()
    sys.exit(0 if success else 1)
