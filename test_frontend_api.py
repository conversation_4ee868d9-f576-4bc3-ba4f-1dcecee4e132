#!/usr/bin/env python3
"""
测试前端API调用
"""
import requests
import json
import os

def test_frontend_api():
    """测试前端API调用"""
    print("=== 测试前端API调用 ===")

    # 设置测试模式
    os.environ["TEST_MODE"] = "true"

    # 测试API端点
    url = "http://localhost:8001/analyze"

    # 使用真实的测试PDF文件
    test_pdf_path = "templates/test_可行性研究报告.pdf"

    try:
        # 准备文件上传
        with open(test_pdf_path, 'rb') as f:
            files = {
                'pdf_file': ('test_可行性研究报告.pdf', f.read(), 'application/pdf')
            }

            print("发送测试请求到API...")
            response = requests.post(url, files=files, timeout=60)

            if response.status_code == 200:
                result = response.json()

                print("✓ API调用成功")
                print(f"返回结果包含的键: {list(result.keys())}")

                # 检查计时统计信息
                if "timing_stats" in result:
                    timing_stats = result["timing_stats"]
                    print(f"\n计时统计信息:")
                    print(f"  执行时间: {timing_stats.get('execution_time', 0)}秒")
                    print(f"  输入Token: {timing_stats.get('total_input_tokens', 0)}")
                    print(f"  输出Token: {timing_stats.get('total_output_tokens', 0)}")
                    print(f"  总Token: {timing_stats.get('total_tokens', 0)}")
                    print(f"  API调用次数: {timing_stats.get('api_calls', 0)}")
                    print(f"  API总耗时: {timing_stats.get('total_api_time', 0)}秒")

                    print("\n✓ 前端API返回计时统计信息正常！")
                else:
                    print("\n✗ 前端API未返回计时统计信息")

                # 检查其他统计信息
                if "statistics" in result:
                    stats = result["statistics"]
                    print(f"\n评审统计信息:")
                    print(f"  总审查细则: {stats.get('total_criteria', 0)}")
                    print(f"  合规率: {stats.get('compliance_rate', 0)}%")
                    print(f"  结果分布: {stats.get('result_distribution', {})}")

                # 输出完整结果用于调试
                print(f"\n完整API响应结构:")
                print(json.dumps(result, ensure_ascii=False, indent=2)[:1000] + "...")

            else:
                print(f"✗ API调用失败，状态码: {response.status_code}")
                print(f"错误信息: {response.text}")

    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()

    # 恢复原始设置
    os.environ["TEST_MODE"] = "false"

    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_frontend_api()
