import PyPDF2
from docx import Document
import pandas as pd
from typing import Dict, List, Any, Tuple
import re

class DocumentParser:
    def __init__(self):
        self.outline = None
        self.review_criteria = None
        self.standard_outline = {
            "1": "概述",
            "2": "项目建设背景和必要性",
            "3": "项目需求分析与预期产出",
            "4": "项目选址与要素保障",
            "5": "项目建设方案",
            "6": "项目运营方案",
            "7": "项目投融资与财务方案",
            "8": "项目影响效果分析",
            "9": "项目风险管控方案",
            "10": "研究结论及建议",
            "11": "附表"
        }
        # 目录相关关键词
        self.toc_keywords = [
            "目录", "目　录", "CONTENTS", "Contents", "contents",
            "TABLE OF CONTENTS", "Table of Contents"
        ]
        # 目录结束标识
        self.toc_end_keywords = [
            "摘要", "前言", "引言", "第一章", "1.", "1 ", "概述"
        ]

    def is_chapter_title(self, line: str) -> tuple[bool, str]:
        """判断是否为章节标题"""
        line = line.strip()

        # 匹配数字编号的章节标题（更灵活的模式）
        chapter_patterns = [
            r'^(\d+)[.、\s]*(.+)$',  # 1概述, 1. 概述, 1 概述
            r'^第(\d+)章[.、\s]*(.+)$',  # 第1章 概述
            r'^(\d+)[.、\s]*(.+?)\.{0,}$'  # 1概述...（去掉末尾的点）
        ]

        for pattern in chapter_patterns:
            match = re.match(pattern, line)
            if match:
                chapter_num = match.group(1)
                chapter_title = match.group(2).strip().rstrip('.')

                # 检查是否与标准大纲匹配（精确匹配或包含匹配）
                if chapter_num in self.standard_outline:
                    standard_title = self.standard_outline[chapter_num]
                    if (chapter_title == standard_title or
                        standard_title in chapter_title or
                        chapter_title in standard_title):
                        return True, f"{chapter_num} {standard_title}"

        # 匹配纯章节标题（不带编号）- 增强版本
        for num, title in self.standard_outline.items():
            # 精确匹配
            if line == title:
                return True, f"{num} {title}"

            # 包含匹配（标题在行中）
            if title in line and len(line) <= len(title) + 10:  # 允许一些额外字符
                return True, f"{num} {title}"

            # 模糊匹配（处理可能的OCR错误）
            if self._fuzzy_match_title(line, title):
                return True, f"{num} {title}"

        # 特殊处理：检查是否包含多个关键词组合
        if self._is_compound_chapter_title(line):
            return self._match_compound_title(line)

        return False, ""

    def _fuzzy_match_title(self, line: str, title: str) -> bool:
        """模糊匹配章节标题（处理OCR错误）"""
        # 移除空格和标点符号进行比较
        line_clean = re.sub(r'[\s\.\,\:\;]', '', line)
        title_clean = re.sub(r'[\s\.\,\:\;]', '', title)

        # 如果清理后的文本匹配
        if line_clean == title_clean:
            return True

        # 如果标题是行的主要部分（占比超过70%）
        if len(title_clean) > 0 and len(line_clean) > 0:
            if title_clean in line_clean and len(title_clean) / len(line_clean) > 0.7:
                return True

        return False

    def _is_compound_chapter_title(self, line: str) -> bool:
        """检查是否是复合章节标题（包含多个关键词）"""
        keywords = ['项目', '建设', '背景', '必要性', '需求', '分析', '预期', '产出',
                   '选址', '要素', '保障', '方案', '运营', '投融资', '财务',
                   '影响', '效果', '风险', '管控', '结论', '建议', '附表']

        found_keywords = [kw for kw in keywords if kw in line]
        return len(found_keywords) >= 2  # 至少包含2个关键词

    def _match_compound_title(self, line: str) -> tuple[bool, str]:
        """匹配复合章节标题"""
        # 根据关键词组合推断章节
        if '背景' in line and '必要性' in line:
            return True, "2 项目建设背景和必要性"
        elif '需求' in line and ('分析' in line or '产出' in line):
            return True, "3 项目需求分析与预期产出"
        elif '选址' in line and ('要素' in line or '保障' in line):
            return True, "4 项目选址与要素保障"
        elif '建设' in line and '方案' in line:
            return True, "5 项目建设方案"
        elif '运营' in line and '方案' in line:
            return True, "6 项目运营方案"
        elif ('投融资' in line or '财务' in line) and '方案' in line:
            return True, "7 项目投融资与财务方案"
        elif '影响' in line and '效果' in line:
            return True, "8 项目影响效果分析"
        elif '风险' in line and '管控' in line:
            return True, "9 项目风险管控方案"
        elif ('结论' in line and '建议' in line) or ('研究' in line and '结论' in line):
            return True, "10 研究结论及建议"
        elif '附表' in line or '附件' in line or '附录' in line:
            return True, "11 附表"

        return False, ""

    def detect_toc_region(self, lines: List[str]) -> tuple[int, int]:
        """检测目录区域的开始和结束位置"""
        toc_start = -1
        toc_end = -1

        # 方法1: 查找明确的目录标识
        for i, line in enumerate(lines):
            line_clean = line.strip()
            if any(keyword in line_clean for keyword in self.toc_keywords):
                toc_start = i
                print(f"检测到目录开始位置: 第{i+1}行 - {line_clean}")
                break

        # 方法2: 如果没找到明确标识，检查前20行是否有目录特征
        if toc_start < 0:
            toc_lines_count = 0
            for i in range(min(20, len(lines))):
                line_clean = lines[i].strip()
                if self._is_toc_line(line_clean):
                    toc_lines_count += 1

            # 如果前20行中有超过5行是目录格式，认为是目录
            if toc_lines_count >= 5:
                toc_start = 0
                print(f"根据格式特征检测到目录开始: 第1行 (目录行数: {toc_lines_count})")

        # 查找目录结束位置
        if toc_start >= 0:
            # 从目录开始位置往后查找
            search_end = min(toc_start + 100, len(lines))  # 扩大搜索范围

            for i in range(toc_start + 1, search_end):
                line_clean = lines[i].strip()

                # 检查是否是目录结束标识
                if any(keyword in line_clean for keyword in self.toc_end_keywords):
                    toc_end = i
                    print(f"检测到目录结束位置: 第{i+1}行 - {line_clean}")
                    break

                # 检查是否是正文章节开始（带有实际内容的章节标题）
                is_title, chapter_title = self.is_chapter_title(line_clean)
                if is_title:
                    # 检查后续几行是否有实际内容（不是目录格式）
                    has_content = False
                    for j in range(i + 1, min(i + 15, len(lines))):
                        next_line = lines[j].strip()
                        if len(next_line) > 20 and not self._is_toc_line(next_line):
                            has_content = True
                            break

                    if has_content:
                        toc_end = i
                        print(f"检测到正文开始位置: 第{i+1}行 - {chapter_title}")
                        break

            # 如果还没找到结束位置，使用启发式方法
            if toc_end < 0:
                # 查找连续非目录行的开始
                non_toc_count = 0
                for i in range(toc_start + 1, search_end):
                    line_clean = lines[i].strip()
                    if not self._is_toc_line(line_clean) and len(line_clean) > 10:
                        non_toc_count += 1
                        if non_toc_count >= 3:  # 连续3行非目录行
                            toc_end = i - 2
                            print(f"启发式检测到目录结束位置: 第{toc_end+1}行")
                            break
                    else:
                        non_toc_count = 0

        return toc_start, toc_end

    def _is_toc_line(self, line: str) -> bool:
        """判断是否是目录行（包含页码、点号等目录特征）"""
        line = line.strip()

        if not line:
            return False

        # 目录行特征：
        # 1. 以数字结尾（页码）
        # 2. 包含连续的点号或破折号
        # 3. 章节标题+页码的格式
        # 4. 短行且包含章节关键词

        # 检查是否以页码结尾
        if re.search(r'\d+\s*$', line):
            return True

        # 检查是否包含目录格式的点号或省略号
        if re.search(r'\.{3,}', line) or re.search(r'…{2,}', line):
            return True

        # 检查章节编号+标题+页码的格式（如：1概述..............1）
        if re.match(r'^\d+[^\d]*\.{3,}\d+\s*$', line):
            return True

        # 检查是否是简短的章节标题行（目录中的标题通常较短且包含章节关键词）
        if len(line) < 80:
            # 检查是否包含章节编号
            if re.match(r'^\d+', line):
                # 检查是否包含标准章节关键词
                for title in self.standard_outline.values():
                    if title in line:
                        return True

            # 检查是否只包含章节关键词（无其他内容）
            for title in self.standard_outline.values():
                if line == title or line.startswith(title):
                    return True

        # 检查是否是纯数字行（页码）
        if line.isdigit() and len(line) <= 3:
            return True

        return False

    def _fix_text_formatting(self, text: str) -> str:
        """修复PDF文本提取中的格式问题"""
        lines = text.split('\n')

        # 检测是否存在字符分割问题（大量单字符行）
        single_char_lines = sum(1 for line in lines if len(line.strip()) == 1)
        total_lines = len([line for line in lines if line.strip()])

        if total_lines > 0 and single_char_lines / total_lines > 0.3:  # 超过30%是单字符行
            print(f"检测到字符分割问题，单字符行比例: {single_char_lines/total_lines:.2%}")
            return self._reconstruct_text_from_chars(lines)
        else:
            # 普通的文本清理
            return self._clean_text_lines(lines)

    def _reconstruct_text_from_chars(self, lines: List[str]) -> str:
        """从字符分割的行重构文本"""
        reconstructed_lines = []
        current_line = []

        for line in lines:
            line = line.strip()
            if not line:
                if current_line:
                    reconstructed_lines.append(''.join(current_line))
                    current_line = []
                continue

            # 如果是单个字符或很短的片段，累积到当前行
            if len(line) <= 3:
                current_line.append(line)
            else:
                # 如果是较长的行，先保存当前累积的内容，然后添加这一行
                if current_line:
                    reconstructed_lines.append(''.join(current_line))
                    current_line = []
                reconstructed_lines.append(line)

        # 处理最后的累积内容
        if current_line:
            reconstructed_lines.append(''.join(current_line))

        # 进一步处理重构的文本，智能分割章节
        final_lines = self._smart_split_chapters(reconstructed_lines)

        print(f"文本重构完成: {len(lines)} -> {len(final_lines)} 行")
        return '\n'.join(final_lines)

    def _smart_split_chapters(self, lines: List[str]) -> List[str]:
        """智能分割章节"""
        final_lines = []

        for line in lines:
            if not line.strip():
                continue

            line = line.strip()

            # 查找所有可能的章节分割点
            split_points = self._find_chapter_split_points(line)

            if split_points:
                # 按分割点分割文本
                last_pos = 0
                for pos in split_points:
                    if pos > last_pos:
                        segment = line[last_pos:pos].strip()
                        if segment and len(segment) > 5:  # 过滤太短的片段
                            final_lines.append(segment)
                    last_pos = pos

                # 添加最后一段
                if last_pos < len(line):
                    segment = line[last_pos:].strip()
                    if segment and len(segment) > 5:
                        final_lines.append(segment)
            else:
                if len(line) > 5:  # 过滤太短的行
                    final_lines.append(line)

        return final_lines

    def _find_chapter_split_points(self, text: str) -> List[int]:
        """查找章节分割点"""
        split_points = []

        # 查找数字+章节标题的模式（更精确的匹配）
        for num, title in self.standard_outline.items():
            # 查找 "数字+标题" 的模式，使用正则表达式确保精确匹配
            patterns = [
                rf'\b{num}{title}\b',
                rf'\b{num}\.{title}\b',
                rf'\b{num}\s+{title}\b',
                rf'第{num}章\s*{title}\b',
                rf'\b{num}\.?\s*{re.escape(title)}'
            ]

            for pattern in patterns:
                for match in re.finditer(pattern, text):
                    split_points.append(match.start())

        # 查找纯标题的模式（用于没有编号的章节）
        for title in self.standard_outline.values():
            # 使用正则表达式查找独立的标题
            pattern = rf'\b{re.escape(title)}\b'
            for match in re.finditer(pattern, text):
                pos = match.start()
                # 检查前后文，确保这是一个独立的标题
                if self._is_standalone_title(text, pos, title):
                    split_points.append(pos)

        # 查找小节标题模式（如1.1、2.1等）
        section_pattern = r'(\d+)\.(\d+)(?:[^\d]|$)'
        for match in re.finditer(section_pattern, text):
            split_points.append(match.start())

        # 查找编号模式（如(1)、（2）等）
        number_pattern = r'[（(]\d+[）)]'
        for match in re.finditer(number_pattern, text):
            split_points.append(match.start())

        # 去重并排序
        split_points = sorted(list(set(split_points)))

        # 过滤太近的分割点（避免过度分割）
        filtered_points = []
        min_distance = 50  # 最小距离增加到50字符
        for point in split_points:
            if not filtered_points or point - filtered_points[-1] >= min_distance:
                filtered_points.append(point)

        return filtered_points

    def _is_standalone_title(self, text: str, pos: int, title: str) -> bool:
        """检查是否是独立的标题"""
        # 检查标题前后的字符
        before_char = text[pos-1] if pos > 0 else ' '
        after_pos = pos + len(title)
        after_char = text[after_pos] if after_pos < len(text) else ' '

        # 如果前后都是空白字符、标点符号或数字，认为是独立标题
        separators = [' ', '\n', '\t', '。', '，', '：', '；', '(', ')', '（', '）', '1', '2', '3', '4', '5', '6', '7', '8', '9', '0']

        return before_char in separators and after_char in separators

    def _clean_text_lines(self, lines: List[str]) -> str:
        """清理普通文本行"""
        cleaned_lines = []
        for line in lines:
            line = line.strip()
            if line and len(line) > 1:  # 过滤掉空行和单字符行
                cleaned_lines.append(line)
        return '\n'.join(cleaned_lines)

    def parse_pdf(self, pdf_path: str) -> Tuple[str,Dict[str, str]]:
        """解析PDF文件，提取章节内容"""
        sections = {f"{num} {title}": "" for num, title in self.standard_outline.items()}
        current_section = None
        current_content = []
        all_text = ""
        project_name = ""

        with open(pdf_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)

            # 先提取所有文本
            for page in reader.pages:
                try:
                    text = page.extract_text()
                    if text.strip():
                        all_text += text + "\n"
                except Exception as e:
                    print(f"页面文本提取失败: {e}")
                    continue

        if not all_text.strip():
            print("PDF文本提取失败，尝试其他方法...")
            # 如果PyPDF2失败，返回空内容但保持结构
            return sections

        # 从pdf文本中提取项目名称
        project_name = self._extract_project_name(all_text)

        # 预处理文本，修复字符分割问题
        all_text = self._fix_text_formatting(all_text)

        lines = all_text.split('\n')
        print(f"提取到 {len(lines)} 行文本")

        # 检测并跳过目录区域
        toc_start, toc_end = self.detect_toc_region(lines)

        if toc_start >= 0 and toc_end >= 0:
            print(f"跳过目录区域: 第{toc_start+1}行到第{toc_end}行")
            # 创建跳过目录的行列表
            filtered_lines = lines[:toc_start] + lines[toc_end:]
        else:
            filtered_lines = lines
            print("未检测到目录区域，处理全部文本")

        # 改进的章节标题匹配
        in_content_area = False  # 标记是否已进入正文区域

        for i, line in enumerate(filtered_lines):
            line = line.strip()
            if not line:
                continue

            is_title, chapter_title = self.is_chapter_title(line)

            if is_title:
                # 验证这是否是真正的章节开始（不是目录中的标题）
                if self._validate_chapter_start(filtered_lines, i):
                    # 保存当前章节内容
                    if current_section and current_content:
                        sections[current_section] = '\n'.join(current_content).strip()
                    current_section = chapter_title
                    current_content = []
                    in_content_area = True
                    print(f"找到章节: {chapter_title}")
                else:
                    print(f"跳过疑似目录中的章节标题: {chapter_title}")
            elif current_section and in_content_area:
                # 过滤掉太短的行、页码、页眉页脚等
                if self._is_valid_content_line(line):
                    current_content.append(line)

        # 保存最后一个章节的内容
        if current_section and current_content:
            sections[current_section] = '\n'.join(current_content).strip()

        # 如果没有找到任何章节，尝试按页面分割内容
        if not any(content.strip() for content in sections.values()):
            print("未找到标准章节，尝试按内容关键词分割...")
            self._extract_by_keywords(all_text, sections)

        return project_name, sections

    def _extract_project_name(self, text: str) -> str:
        """从文本中提取项目名称"""
        lines = text.split('\n')
        project_name_pattern = re.compile(r'^项目名称:\s*(.*)$')

        for line in lines:
            match = project_name_pattern.match(line.strip())
            if match:
                return match.group(1).strip()

        return ""
    def _validate_chapter_start(self, lines: List[str], index: int) -> bool:
        """验证是否是真正的章节开始"""
        # 检查后续几行是否有实际内容
        content_lines = 0
        for i in range(index + 1, min(index + 15, len(lines))):
            line = lines[i].strip()
            if self._is_valid_content_line(line):
                content_lines += 1
                if content_lines >= 2:  # 至少有2行有效内容
                    return True

        return False

    def _is_valid_content_line(self, line: str) -> bool:
        """判断是否是有效的内容行"""
        line = line.strip()

        # 过滤条件
        if len(line) < 5:  # 太短的行
            return False

        if line.isdigit():  # 纯数字（页码）
            return False

        if re.match(r'^[\d\-\s]+$', line):  # 只包含数字、横线、空格
            return False

        # 常见的页眉页脚模式
        footer_patterns = [
            r'^\d+$',  # 页码
            r'^第\s*\d+\s*页',  # 第X页
            r'^\d+\s*/\s*\d+$',  # X/Y页码格式
            r'^[\d\-]+$',  # 纯数字和横线
        ]

        for pattern in footer_patterns:
            if re.match(pattern, line):
                return False

        # 检查是否是目录行
        if self._is_toc_line(line):
            return False

        return True

    def _extract_by_keywords(self, text: str, sections: Dict[str, str]):
        """根据关键词提取章节内容"""
        keywords_map = {
            "1 概述": ["概述", "项目概况", "总体情况", "基本情况"],
            "2 项目建设背景和必要性": ["建设背景", "必要性", "项目背景", "建设必要性"],
            "3 项目需求分析与预期产出": ["需求分析", "预期产出", "需求", "产出"],
            "4 项目选址与要素保障": ["选址", "要素保障", "建设条件", "选址条件"],
            "5 项目建设方案": ["建设方案", "技术方案", "工程方案", "实施方案"],
            "6 项目运营方案": ["运营方案", "运营管理", "运行方案", "管理方案"],
            "7 项目投融资与财务方案": ["投融资", "财务方案", "投资估算", "资金筹措"],
            "8 项目影响效果分析": ["影响效果", "效益分析", "社会效益", "经济效益"],
            "9 项目风险管控方案": ["风险管控", "风险分析", "风险防范", "风险评估"],
            "10 研究结论及建议": ["研究结论", "建议", "结论", "总结"],
            "11 附表": ["附表", "附件", "附录", "表格"]
        }

        # 按段落分割文本，并尝试智能分配到章节
        lines = text.split('\n')
        current_paragraph = []

        for line in lines:
            line = line.strip()
            if not line:
                if current_paragraph:
                    paragraph_text = '\n'.join(current_paragraph)
                    self._assign_paragraph_to_section(paragraph_text, keywords_map, sections)
                    current_paragraph = []
            else:
                if self._is_valid_content_line(line):
                    current_paragraph.append(line)

        # 处理最后一个段落
        if current_paragraph:
            paragraph_text = '\n'.join(current_paragraph)
            self._assign_paragraph_to_section(paragraph_text, keywords_map, sections)

    def _assign_paragraph_to_section(self, paragraph: str, keywords_map: Dict[str, List[str]], sections: Dict[str, str]):
        """将段落分配到合适的章节"""
        if len(paragraph.strip()) < 30:  # 跳过太短的段落
            return

        # 首先检查段落开头是否有章节标识
        first_line = paragraph.split('\n')[0].strip()
        is_title, chapter_title = self.is_chapter_title(first_line)

        if is_title:
            # 如果段落以章节标题开始，将整个段落分配到该章节
            if sections[chapter_title]:
                sections[chapter_title] += "\n\n" + paragraph.strip()
            else:
                sections[chapter_title] = paragraph.strip()
            print(f"段落分配到 {chapter_title} (章节标题匹配)")
            return

        # 计算每个章节的匹配分数
        best_section = None
        best_score = 0

        for section_name, keywords in keywords_map.items():
            score = 0
            for keyword in keywords:
                # 关键词匹配计分
                count = paragraph.count(keyword)
                if count > 0:
                    score += len(keyword) * count  # 长关键词权重更高，出现次数也影响分数

            # 额外检查：如果段落包含章节编号
            section_num = section_name.split()[0]
            if f"{section_num}." in paragraph or f"{section_num} " in paragraph:
                score += 10  # 章节编号匹配给额外分数

            if score > best_score:
                best_score = score
                best_section = section_name

        # 如果有匹配的章节，分配段落
        if best_section and best_score > 0:
            if sections[best_section]:
                sections[best_section] += "\n\n" + paragraph.strip()
            else:
                sections[best_section] = paragraph.strip()
            print(f"段落分配到 {best_section} (匹配分数: {best_score})")
        else:
            # 如果没有明确匹配，尝试根据位置分配
            # 检查段落是否包含项目基本信息
            if any(keyword in paragraph.lower() for keyword in ['项目名称', '建设目标', '建设地点', '项目概况']):
                target_section = "1 概述"
            else:
                target_section = "1 概述"  # 默认分配到概述

            if sections[target_section]:
                sections[target_section] += "\n\n" + paragraph.strip()
            else:
                sections[target_section] = paragraph.strip()
            print(f"段落分配到默认章节: {target_section}")

    def parse_outline(self, word_path: str) -> Dict[str, str]:
        """解析Word文档中的大纲，只解析第一级章节标题，章节内容全部作为一个文本"""
        doc = Document(word_path)
        outline = {}
        current_chapter = None
        current_content = []

        for para in doc.paragraphs:
            text = para.text.strip()
            if not text:
                continue

            # 只识别主要章节标题（1-11章）
            is_main_title, chapter_title = self._is_main_chapter_title(text)

            if is_main_title:
                # 保存上一个章节的内容
                if current_chapter and current_content:
                    outline[current_chapter] = '\n'.join(current_content)

                # 开始新章节
                current_chapter = chapter_title
                current_content = []
                # print(f"找到主章节: {chapter_title}")
            elif current_chapter and text:
                # 将所有内容（包括子标题）都添加到当前章节
                current_content.append(text)

        # 保存最后一个章节的内容
        if current_chapter and current_content:
            outline[current_chapter] = '\n'.join(current_content)

        # 验证每个章节都有足够的内容
        # self._validate_outline_content_string(outline)

        self.outline = outline
        return outline

    def _is_main_chapter_title(self, text: str) -> tuple[bool, str]:
        """判断是否为主要章节标题（1-11章），不包括子标题"""
        text = text.strip()

        # 匹配主要章节编号的模式（只匹配1-11的单数字）
        main_chapter_patterns = [
            r'^(\d{1,2})[.、\s]*(.+)$',  # 1概述, 1. 概述, 1 概述, 10 研究结论及建议
            r'^第(\d{1,2})章[.、\s]*(.+)$',  # 第1章 概述
        ]

        for pattern in main_chapter_patterns:
            match = re.match(pattern, text)
            if match:
                chapter_num = match.group(1)
                chapter_title = match.group(2).strip().rstrip('.')

                # 只处理1-11章的主要章节
                if chapter_num in self.standard_outline:
                    standard_title = self.standard_outline[chapter_num]
                    if (chapter_title == standard_title or
                        standard_title in chapter_title or
                        chapter_title in standard_title):
                        return True, f"{chapter_num} {standard_title}"

        # 匹配纯章节标题（不带编号）
        for num, title in self.standard_outline.items():
            if text == title or (title in text and len(text) <= len(title) + 10):
                return True, f"{num} {title}"

        return False, ""

    def _is_sub_title(self, text: str) -> bool:
        """判断是否为子标题（如1.1、1.2等），需要过滤掉"""
        text = text.strip()

        # 子标题模式
        sub_title_patterns = [
            r'^\d+\.\d+',  # 1.1, 1.2, 2.1 等
            r'^\(\d+\)',   # (1), (2) 等
            r'^（\d+）',   # （1）, （2） 等
            r'^\d+）',     # 1）, 2） 等
            r'^[a-zA-Z]\.',  # a., b., A., B. 等
        ]

        for pattern in sub_title_patterns:
            if re.match(pattern, text):
                return True

        # 如果文本很短且只包含数字和少量文字，可能是子标题
        if len(text) < 20 and re.match(r'^[\d\.\s\u4e00-\u9fa5]{1,15}$', text):
            # 检查是否包含常见的子标题关键词
            sub_keywords = ['概况', '简述', '说明', '分析', '计算', '确定', '选择']
            if any(keyword in text for keyword in sub_keywords):
                return True

        return False

    def _validate_outline_content_string(self, outline: Dict[str, str]) -> None:
        """验证大纲内容（字符串格式），确保每个章节都有足够的内容"""
        print(f"\n{'='*40} 验证大纲内容 {'='*40}")

        for chapter_title, content in outline.items():
            content_length = len(content) if content else 0
            print(f"章节 '{chapter_title}': {content_length} 字符")

            if content_length < 30:
                print(f"⚠️  章节 '{chapter_title}' 内容不足（少于30字符）")
            else:
                print(f"✓ 章节 '{chapter_title}' 内容充足")

            # 显示内容预览
            if content:
                preview = content[:100] + "..." if len(content) > 100 else content
                print(f"    内容预览: {preview}")

    def _validate_outline_content(self, outline: Dict[str, Any]) -> None:
        """验证大纲内容，确保每个章节都有足够的内容"""
        print(f"\n{'='*40} 验证大纲内容 {'='*40}")

        for chapter_title, content in outline.items():
            if isinstance(content, list):
                total_chars = sum(len(item) for item in content)
                print(f"章节 '{chapter_title}': {len(content)} 个段落, {total_chars} 字符")

                if total_chars < 30:
                    print(f"⚠️  章节 '{chapter_title}' 内容不足（少于30字符）")
                else:
                    print(f"✓ 章节 '{chapter_title}' 内容充足")
            else:
                content_length = len(str(content)) if content else 0
                print(f"章节 '{chapter_title}': {content_length} 字符")

                if content_length < 30:
                    print(f"⚠️  章节 '{chapter_title}' 内容不足（少于30字符）")
                else:
                    print(f"✓ 章节 '{chapter_title}' 内容充足")

    def get_chapter_outline(self, outline: Dict[str, str], section_title: str) -> str:
        """根据章节标题获取对应的大纲要求"""
        if not outline:
            return "未提供大纲信息"

        # 直接匹配章节标题
        if section_title in outline:
            return outline[section_title]

        # 模糊匹配：查找包含关键词的章节
        for outline_title, outline_content in outline.items():
            # 提取章节关键词进行匹配
            section_keywords = self._extract_chapter_keywords(section_title)
            outline_keywords = self._extract_chapter_keywords(outline_title)

            # 如果有关键词匹配，返回该章节大纲
            if any(keyword in outline_keywords for keyword in section_keywords):
                return outline_content

        # 如果没有找到匹配的章节，返回所有大纲信息
        all_outline = []
        for title, content in outline.items():
            all_outline.append(f"【{title}】\n{content}")

        return "未找到对应章节的大纲要求，以下是完整大纲信息：\n" + "\n\n".join(all_outline)

    def _extract_chapter_keywords(self, title: str) -> list:
        """提取章节标题的关键词"""
        import re
        # 移除数字编号和标点符号，提取关键词
        cleaned_title = re.sub(r'^\d+\.?\s*', '', title)  # 移除开头的数字编号
        cleaned_title = re.sub(r'[^\u4e00-\u9fa5a-zA-Z]', ' ', cleaned_title)  # 只保留中英文
        keywords = [word.strip() for word in cleaned_title.split() if len(word.strip()) > 1]
        return keywords

    def parse_review_guide(self, file_path: str) -> str:
        """解析审查指南文档"""
        try:
            doc = Document(file_path)
            guide_content = []

            for paragraph in doc.paragraphs:
                text = paragraph.text.strip()
                if text:
                    guide_content.append(text)

            result = '\n'.join(guide_content)
            print(f"解析审查指南完成，内容长度: {len(result)} 字符")
            return result

        except Exception as e:
            print(f"解析审查指南文件失败: {e}")
            return "审查指南解析失败"

    def parse_review_criteria(self, excel_path: str) -> List[Dict[str, Any]]:
        """解析Excel文件中的审查细则"""
        df = pd.read_excel(excel_path)
        criteria = []
        current_criterion_id = None
        criterion_counter = 0

        for index, row in df.iterrows():
            # 跳过完全空的行
            if (pd.isna(row.get('序号', '')) and
                pd.isna(row.get('审查细则', '')) and
                pd.isna(row.get('审查范畴', ''))):
                continue

            # 获取审查细则内容
            content = str(row.get('审查细则', '')).strip() if not pd.isna(row.get('审查细则', '')) else ''
            requirements = str(row.get('审查范畴', '')).strip() if not pd.isna(row.get('审查范畴', '')) else ''

            # 跳过没有实际内容的行
            if not content or content in ['nan', 'NaN', '']:
                continue

            # 处理序号列（可能是合并单元格）
            sequence_num = row.get('序号', '')
            if not pd.isna(sequence_num) and str(sequence_num).strip():
                # 如果序号不为空，更新当前审查细则ID
                current_criterion_id = str(sequence_num).strip()
                criterion_counter += 1
            else:
                # 如果序号为空（合并单元格），使用当前的审查细则ID
                # 如果还没有当前ID，使用行号
                if current_criterion_id is None:
                    criterion_counter += 1
                    current_criterion_id = str(criterion_counter)

            # 使用行号作为最终的criterion_id，确保唯一性
            final_criterion_id = f"{criterion_counter}.{index + 1}"

            criteria.append({
                'id': final_criterion_id,
                'original_id': current_criterion_id,
                'content': content,
                'requirements': requirements,
                'row_index': index + 1
            })

            #print(f"解析审查细则 {len(criteria)}: ID={final_criterion_id}, 原始ID={current_criterion_id}, 内容={content[:30]}...")

        print(f"总共解析到 {len(criteria)} 个有效的审查细则")
        self.review_criteria = criteria
        return criteria