#!/usr/bin/env python3
"""
简单的章节分析测试
"""
import sys
import os
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.append('.')

# 加载环境变量
load_dotenv()

def test_basic_functionality():
    """测试基本功能"""
    print("开始基本功能测试...")
    
    try:
        # 1. 测试导入
        print("1. 测试模块导入...")
        from services.document_parser import DocumentParser
        from services.model_service import ModelService
        from services.report_analyzer import ReportAnalyzer
        print("✓ 模块导入成功")
        
        # 2. 测试初始化
        print("2. 测试服务初始化...")
        document_parser = DocumentParser()
        model_service = ModelService()
        report_analyzer = ReportAnalyzer(model_service, document_parser)
        print("✓ 服务初始化成功")
        
        # 3. 测试模板加载
        print("3. 测试模板加载...")
        report_analyzer._load_templates()
        print(f"✓ 大纲: {len(report_analyzer.outline)} 章节")
        print(f"✓ 审查细则: {len(report_analyzer.criteria)} 项")
        print(f"✓ 审查指南: {len(report_analyzer.review_guide)} 字符")
        
        # 4. 显示可用章节
        print("4. 可用章节:")
        for i, section in enumerate(report_analyzer.outline.keys(), 1):
            print(f"   {i}. {section}")
        
        # 5. 测试PDF文件是否存在
        pdf_path = "templates/test_可行性研究报告.pdf"
        print(f"5. 检查PDF文件: {pdf_path}")
        if os.path.exists(pdf_path):
            print("✓ PDF文件存在")
            
            # 6. 测试PDF解析（不调用模型）
            print("6. 测试PDF解析...")
            project_name, pdf_sections = document_parser.parse_pdf(pdf_path)
            if not project_name:
                project_name = os.path.splitext(os.path.basename(pdf_path))[0]
            print(f"✓ 项目名称: {project_name}")
            print(f"✓ 提取章节数: {len(pdf_sections)}")
            
            # 显示PDF中的章节
            print("PDF中的章节:")
            for i, (title, content) in enumerate(pdf_sections.items(), 1):
                print(f"   {i}. {title} ({len(content)} 字符)")
                
        else:
            print("❌ PDF文件不存在")
        
        print("\n✅ 基本功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_section_content_extraction():
    """测试章节内容提取"""
    print("\n开始章节内容提取测试...")
    
    try:
        from services.document_parser import DocumentParser
        from services.report_analyzer import ReportAnalyzer
        from services.model_service import ModelService
        
        # 初始化
        document_parser = DocumentParser()
        model_service = ModelService()
        report_analyzer = ReportAnalyzer(model_service, document_parser)
        report_analyzer._load_templates()
        
        # 解析PDF
        pdf_path = "templates/test_可行性研究报告.pdf"
        if not os.path.exists(pdf_path):
            print("❌ PDF文件不存在")
            return False
            
        project_name, pdf_sections = document_parser.parse_pdf(pdf_path)
        
        # 测试章节内容提取
        target_section = "1 概述"
        if target_section in report_analyzer.outline:
            print(f"测试提取章节: {target_section}")
            
            section_content = report_analyzer._extract_section_content_from_pdf(
                target_section, pdf_sections
            )
            
            print(f"✓ 提取内容长度: {len(section_content)} 字符")
            print(f"内容预览: {section_content[:200]}...")
            
            # 获取大纲要求
            chapter_outline = report_analyzer.outline[target_section]
            print(f"✓ 大纲要求长度: {len(chapter_outline)} 字符")
            print(f"大纲预览: {chapter_outline[:200]}...")
            
            print("✅ 章节内容提取测试完成")
            return True
        else:
            print(f"❌ 章节 {target_section} 不存在于大纲中")
            return False
            
    except Exception as e:
        print(f"❌ 章节内容提取测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("="*60)
    print("简单章节分析测试")
    print("="*60)
    
    # 基本功能测试
    basic_ok = test_basic_functionality()
    
    if basic_ok:
        # 章节内容提取测试
        extraction_ok = test_section_content_extraction()
        
        if extraction_ok:
            print(f"\n{'='*60}")
            print("所有测试通过！")
            print("注意：由于模型调用可能较慢，实际的模型分析测试需要单独进行")
            print(f"{'='*60}")
        else:
            print("\n❌ 章节内容提取测试失败")
    else:
        print("\n❌ 基本功能测试失败")

if __name__ == "__main__":
    main()
